export declare enum UserTypes {
    USER = "user",
    ADMIN = "admin"
}
export declare enum PodcastTypes {
    TECHNOLOGY = "Technology",
    EDUCATION = "Education",
    HEALTH = "Health",
    BUSINESS = "Business",
    ENTERTAINMENT = "Entertainment",
    OTHER = "Other"
}
export type UserRole = UserTypes;
export type PodcastCategory = PodcastTypes;
export declare const Constants: {
    readonly UserTypes: typeof UserTypes;
    readonly PodcastTypes: typeof PodcastTypes;
};
export default Constants;
//# sourceMappingURL=constants.d.ts.map