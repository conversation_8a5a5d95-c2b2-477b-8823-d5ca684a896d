{"version": 3, "sources": ["../../src/lib/normalize-path.ts"], "sourcesContent": ["import path from 'path'\n\nexport function normalizePath(file: string) {\n  return path.sep === '\\\\' ? file.replace(/\\\\/g, '/') : file\n}\n"], "names": ["normalizePath", "file", "path", "sep", "replace"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;6DAFC;;;;;;AAEV,SAASA,cAAcC,IAAY;IACxC,OAAOC,aAAI,CAACC,GAAG,KAAK,OAAOF,KAAKG,OAAO,CAAC,OAAO,OAAOH;AACxD", "ignoreList": [0]}