import mongoose, { Document, Types } from "mongoose";
import { PodcastCategory } from "../utils/constants.js";
export interface IPodcast extends Document {
    _id: Types.ObjectId;
    title: string;
    description: string;
    author: string;
    category: PodcastCategory;
    coverImageUrl?: string;
    episodes: Types.ObjectId[];
    followers: Types.ObjectId[];
    createdAt: Date;
    updatedAt: Date;
}
export interface IPodcastInput {
    title: string;
    description: string;
    author: string;
    category?: PodcastCategory;
    coverImageUrl?: string;
    episodes?: Types.ObjectId[];
    followers?: Types.ObjectId[];
}
export declare const Podcast: mongoose.Model<IPodcast, {}, {}, {}, mongoose.Document<unknown, {}, IPodcast, {}, {}> & IPodcast & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=podcast.d.ts.map