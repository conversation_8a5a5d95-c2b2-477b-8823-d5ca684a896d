# 🎙️ Podcast Hub Backend (TypeScript)

A robust TypeScript backend for the Podcast Hub application built with Express.js, MongoDB, and Mongoose.

## 🏗️ Tech Stack

- **Runtime**: Node.js 18+
- **Language**: TypeScript 5.x
- **Framework**: Express.js 5.x
- **Database**: MongoDB with Mongoose ODM
- **Development**: ts-node, nodemon
- **Type Safety**: Strict TypeScript configuration

## 📁 Project Structure

```
Backend/
├── src/
│   ├── models/          # Mongoose models with TypeScript interfaces
│   │   ├── user.ts      # User model with validation
│   │   ├── podcast.ts   # Podcast model with pagination
│   │   ├── episode.ts   # Episode model with virtuals
│   │   └── index.ts     # Model exports
│   ├── controllers/     # Route controllers
│   ├── routes/          # Express routes
│   ├── middlewares/     # Custom middleware
│   ├── db/              # Database configuration
│   │   └── db.ts        # MongoDB connection with error handling
│   ├── utils/           # Utility functions and constants
│   │   └── constants.ts # Enums and type definitions
│   ├── app.ts           # Express app configuration
│   └── server.ts        # Server startup and configuration
├── dist/                # Compiled JavaScript (auto-generated)
├── tsconfig.json        # TypeScript configuration
├── nodemon.json         # Nodemon configuration
└── package.json         # Dependencies and scripts
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- MongoDB (local or Atlas)

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   MONGODB_URI=mongodb://localhost:27017/podcast-hub
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:3000
   ```

### Development

1. **Start development server** (with hot reload):
   ```bash
   npm run dev:watch
   ```

2. **Run development server** (single run):
   ```bash
   npm run dev
   ```

3. **Type checking**:
   ```bash
   npm run type-check
   ```

### Production

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Start production server**:
   ```bash
   npm start
   ```

## 📊 Database Models

### User Model
- **Fields**: name, email, password, favorites, recentlyPlayed, avatarUrl, role
- **Features**: Email validation, password hashing ready, role-based access
- **Indexes**: email, role

### Podcast Model
- **Fields**: title, description, author, category, coverImageUrl, episodes, followers
- **Features**: Text search, pagination, virtual fields for counts
- **Indexes**: text search, category, author, createdAt

### Episode Model
- **Fields**: title, description, audioUrl, duration, podcast, publishedAt, playCount
- **Features**: Duration formatting, relative dates, play count tracking
- **Indexes**: podcast+publishedAt, text search, publishedAt, playCount

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with ts-node |
| `npm run dev:watch` | Start development server with auto-reload |
| `npm run build` | Compile TypeScript to JavaScript |
| `npm start` | Start production server |
| `npm run clean` | Remove dist folder |
| `npm run type-check` | Run TypeScript type checking |

## 🛡️ Type Safety Features

- **Strict TypeScript**: Full type checking enabled
- **Interface Definitions**: Comprehensive interfaces for all models
- **Enum Constants**: Type-safe enums for user roles and podcast categories
- **Input Validation**: Mongoose schema validation with TypeScript types
- **Error Handling**: Typed error responses

## 🔍 API Endpoints

### Health Check
- `GET /health` - Server health status

### Future Endpoints
- User management (registration, login, profile)
- Podcast CRUD operations
- Episode management
- Search and filtering
- User favorites and recently played

## 🌟 Key Features

1. **Type Safety**: Full TypeScript implementation with strict checking
2. **Database Validation**: Comprehensive Mongoose schema validation
3. **Error Handling**: Graceful error handling with proper logging
4. **Development Experience**: Hot reload, type checking, and debugging support
5. **Production Ready**: Optimized build process and environment configuration
6. **Scalable Architecture**: Modular structure for easy expansion

## 🔧 Configuration

### TypeScript Configuration
- Target: ES2020
- Module: ESNext with Node resolution
- Strict mode enabled
- Path mapping for clean imports
- Source maps and declarations generated

### Development Tools
- **nodemon**: Auto-restart on file changes
- **ts-node**: Direct TypeScript execution
- **Source maps**: Debug TypeScript directly

## 📝 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MONGODB_URI` | MongoDB connection string | Required |
| `PORT` | Server port | 5000 |
| `NODE_ENV` | Environment mode | development |
| `FRONTEND_URL` | Frontend URL for CORS | * (dev) |

## 🤝 Contributing

1. Follow TypeScript best practices
2. Add proper type definitions for new features
3. Include validation in Mongoose schemas
4. Write meaningful commit messages
5. Test your changes thoroughly

## 📄 License

ISC License - see package.json for details
