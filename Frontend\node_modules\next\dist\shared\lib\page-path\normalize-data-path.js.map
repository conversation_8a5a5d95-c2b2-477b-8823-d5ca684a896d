{"version": 3, "sources": ["../../../../src/shared/lib/page-path/normalize-data-path.ts"], "sourcesContent": ["import { pathHasPrefix } from '../router/utils/path-has-prefix'\n\n/**\n * strip _next/data/<build-id>/ prefix and .json suffix\n */\nexport function normalizeDataPath(pathname: string) {\n  if (!pathHasPrefix(pathname || '/', '/_next/data')) {\n    return pathname\n  }\n  pathname = pathname\n    .replace(/\\/_next\\/data\\/[^/]{1,}/, '')\n    .replace(/\\.json$/, '')\n\n  if (pathname === '/index') {\n    return '/'\n  }\n  return pathname\n}\n"], "names": ["normalizeDataPath", "pathname", "pathHasPrefix", "replace"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;+BALc;AAKvB,SAASA,kBAAkBC,QAAgB;IAChD,IAAI,CAACC,IAAAA,4BAAa,EAACD,YAAY,KAAK,gBAAgB;QAClD,OAAOA;IACT;IACAA,WAAWA,SACRE,OAAO,CAAC,2BAA2B,IACnCA,OAAO,CAAC,WAAW;IAEtB,IAAIF,aAAa,UAAU;QACzB,OAAO;IACT;IACA,OAAOA;AACT", "ignoreList": [0]}