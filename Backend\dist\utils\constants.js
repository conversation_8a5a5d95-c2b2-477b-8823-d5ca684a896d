// User Types Enum
export var UserTypes;
(function (UserTypes) {
    UserTypes["USER"] = "user";
    UserTypes["ADMIN"] = "admin";
})(UserTypes || (UserTypes = {}));
// Podcast Types Enum
export var PodcastTypes;
(function (PodcastTypes) {
    PodcastTypes["TECHNOLOGY"] = "Technology";
    PodcastTypes["EDUCATION"] = "Education";
    PodcastTypes["HEALTH"] = "Health";
    PodcastTypes["BUSINESS"] = "Business";
    PodcastTypes["ENTERTAINMENT"] = "Entertainment";
    PodcastTypes["OTHER"] = "Other";
})(PodcastTypes || (PodcastTypes = {}));
// Constants object (for backward compatibility if needed)
export const Constants = {
    UserTypes,
    PodcastTypes,
};
export default Constants;
//# sourceMappingURL=constants.js.map