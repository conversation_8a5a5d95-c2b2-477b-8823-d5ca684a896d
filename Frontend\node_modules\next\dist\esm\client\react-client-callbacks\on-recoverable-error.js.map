{"version": 3, "sources": ["../../../src/client/react-client-callbacks/on-recoverable-error.ts"], "sourcesContent": ["// This module can be shared between both pages router and app router\n\nimport type { HydrationOptions } from 'react-dom/client'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport isError from '../../lib/is-error'\nimport { reportGlobalError } from './report-global-error'\n\nconst recoverableErrors = new WeakSet<Error>()\n\nexport function isRecoverableError(error: Error): boolean {\n  return recoverableErrors.has(error)\n}\n\nexport const onRecoverableError: HydrationOptions['onRecoverableError'] = (\n  error\n) => {\n  // x-ref: https://github.com/facebook/react/pull/28736\n  let cause = isError(error) && 'cause' in error ? error.cause : error\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(cause)) return\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { decorateDevError } =\n      require('../../next-devtools/userspace/app/errors/stitched-error') as typeof import('../../next-devtools/userspace/app/errors/stitched-error')\n    const causeError = decorateDevError(cause)\n    recoverableErrors.add(causeError)\n    cause = causeError\n  }\n\n  reportGlobalError(cause)\n}\n"], "names": ["isBailoutToCSRError", "isError", "reportGlobalError", "recoverableErrors", "WeakSet", "isRecoverableError", "error", "has", "onRecoverableError", "cause", "process", "env", "NODE_ENV", "decorateDevError", "require", "causeError", "add"], "mappings": "AAAA,qEAAqE;AAGrE,SAASA,mBAAmB,QAAQ,+CAA8C;AAClF,OAAOC,aAAa,qBAAoB;AACxC,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,MAAMC,oBAAoB,IAAIC;AAE9B,OAAO,SAASC,mBAAmBC,KAAY;IAC7C,OAAOH,kBAAkBI,GAAG,CAACD;AAC/B;AAEA,OAAO,MAAME,qBAA6D,CACxEF;IAEA,sDAAsD;IACtD,IAAIG,QAAQR,QAAQK,UAAU,WAAWA,QAAQA,MAAMG,KAAK,GAAGH;IAC/D,6EAA6E;IAC7E,IAAIN,oBAAoBS,QAAQ;IAEhC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QACV,MAAMC,aAAaF,iBAAiBJ;QACpCN,kBAAkBa,GAAG,CAACD;QACtBN,QAAQM;IACV;IAEAb,kBAAkBO;AACpB,EAAC", "ignoreList": [0]}