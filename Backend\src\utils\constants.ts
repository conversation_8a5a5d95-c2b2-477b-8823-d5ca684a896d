// User Types Enum
export enum UserTypes {
  USER = "user",
  ADMIN = "admin",
}

// Podcast Types Enum
export enum PodcastTypes {
  TECHNOLOGY = "Technology",
  EDUCATION = "Education",
  HEALTH = "Health",
  BUSINESS = "Business",
  ENTERTAINMENT = "Entertainment",
  OTHER = "Other",
}

// Type definitions for better type safety
export type UserRole = UserTypes;
export type PodcastCategory = PodcastTypes;

// Constants object 
export const Constants = {
  UserTypes,
  PodcastTypes,
} as const;

export default Constants;
