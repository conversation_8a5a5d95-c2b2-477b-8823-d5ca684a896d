declare namespace _default {
    export { getAllPodcasts };
    export { getPodcastById };
    export { searchAndFilterByTitleOrCategory };
}
export default _default;
/**
 * @desc Get all podcasts (paginated)
 * @route GET /api/podcasts
 * @access Public
 */
declare function getAllPodcasts(req: any, res: any): Promise<void>;
/**
 * @desc Get a single podcast by ID (with its episodes)
 * @route GET /api/podcasts/:id
 * @access Public
 */
declare function getPodcastById(req: any, res: any): Promise<any>;
/**
 * @desc Search podcasts by title or filter by category (paginated)
 * @route GET /api/podcasts/search?query=react&category=Technology&page=1&limit=5
 * @access Public
 */
declare function searchAndFilterByTitleOrCategory(req: any, res: any): Promise<void>;
//# sourceMappingURL=podcast.d.ts.map