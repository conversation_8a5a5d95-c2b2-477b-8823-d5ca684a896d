{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node --esm src/server.ts", "dev:watch": "nodemon --exec \"ts-node --esm\" src/server.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "type-check": "tsc --noEmit", "lint": "echo \"Add ESLint configuration for linting\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["podcast", "api", "typescript", "express", "mongodb"], "author": "", "license": "ISC", "description": "TypeScript backend for Podcast Hub application", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.3", "express": "^5.1.0", "mongoose": "^8.19.0", "mongoose-paginate-v2": "^1.9.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/mongoose": "^5.11.96", "@types/node": "^24.6.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}