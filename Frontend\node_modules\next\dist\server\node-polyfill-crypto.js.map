{"version": 3, "sources": ["../../src/server/node-polyfill-crypto.ts"], "sourcesContent": ["// Polyfill crypto() in the Node.js environment\n\nif (!global.crypto) {\n  let webcrypto: Crypto | undefined\n\n  Object.defineProperty(global, 'crypto', {\n    enumerable: false,\n    configurable: true,\n    get() {\n      if (!webcrypto) {\n        // @ts-expect-error -- TODO: Is this actually safe?\n        webcrypto = (require('node:crypto') as typeof import('node:crypto'))\n          .webcrypto\n      }\n      return webcrypto\n    },\n    set(value: Crypto) {\n      webcrypto = value\n    },\n  })\n}\n"], "names": ["global", "crypto", "webcrypto", "Object", "defineProperty", "enumerable", "configurable", "get", "require", "set", "value"], "mappings": "AAAA,+CAA+C;;AAE/C,IAAI,CAACA,OAAOC,MAAM,EAAE;IAClB,IAAIC;IAEJC,OAAOC,cAAc,CAACJ,QAAQ,UAAU;QACtCK,YAAY;QACZC,cAAc;QACdC;YACE,IAAI,CAACL,WAAW;gBACd,mDAAmD;gBACnDA,YAAY,AAACM,QAAQ,eAClBN,SAAS;YACd;YACA,OAAOA;QACT;QACAO,KAAIC,KAAa;YACfR,YAAYQ;QACd;IACF;AACF", "ignoreList": [0]}