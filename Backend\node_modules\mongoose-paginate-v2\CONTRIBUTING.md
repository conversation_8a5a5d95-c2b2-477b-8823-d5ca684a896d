# Contributing to Mongoose Paginate v2

First off, thank you for considering contributing to Mongoose Paginate v2! It's people like you that make open-source projects such a fantastic place to learn, inspire, and create. By contributing, you agree to uphold this code of conduct.

## How Can I Contribute?

### Reporting Bugs

If you find a bug, please report it by [opening a new issue](https://github.com/aravindnc/mongoose-paginate-v2/issues).

When you report a bug, please include:
- A clear and descriptive title.
- A description of the bug, including steps to reproduce, expected behavior, and actual behavior.
- Any relevant logs, screenshots, or context that could help diagnose the issue.

### Suggesting Enhancements

If you have an idea for an enhancement, please [open a new issue](https://github.com/aravindnc/mongoose-paginate-v2/issues) and describe your suggestion.

When suggesting an enhancement, please include:
- A clear and descriptive title.
- A detailed description of your proposed enhancement, including any relevant examples or use cases.
- Any potential drawbacks or limitations.

### Submitting Pull Requests

To contribute code or documentation, please follow these steps:

1. **Fork the repository** to your own GitHub account.

2. **Clone the forked repository** to your local machine.

    ```sh
    git clone https://github.com/YOUR_USERNAME/mongoose-paginate-v2.git
    cd mongoose-paginate-v2
    ```

3. **Create a new branch** for your changes.

    ```sh
    git checkout -b feature/your-feature-name
    ```

4. **Make your changes** in your local repository.

5. **Commit your changes** with a clear and descriptive commit message.

    ```sh
    git add .
    git commit -m "Add feature or fix description"
    ```

6. **Push your changes** to your forked repository.

    ```sh
    git push origin feature/your-feature-name
    ```

7. **Open a pull request** against the `main` branch of the original repository.

In your pull request, please include:
- A clear and descriptive title.
- A detailed description of your changes, including the problem your changes are solving and any relevant details about your implementation.
- Any relevant issues or pull requests that your changes are related to.

## Code Style

Please ensure that your code adheres to the project's coding style and conventions.

## Running Tests
Please ensure that all tests pass before submitting a pull request. You can run tests with:

```sh
npm run test
```

If you add new functionality, please include appropriate tests.

## Code of Conduct
This project adheres to a Code of Conduct. By participating, you are expected to uphold this code. Please raise issue for any unacceptable behavior.

Thank you for contributing!

[@aravindnc](https://github.com/aravindnc)

Feel free to adjust any sections to better fit your project's needs.
