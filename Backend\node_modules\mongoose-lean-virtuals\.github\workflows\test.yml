name: Test
on:
  pull_request:
  push:
permissions:
  contents: read

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        node: [16, 18, 20, 22]
        os: [ubuntu-22.04]
        mongo-os: [ubuntu2204]
        mongo: [7.0.12]
    name: Node ${{ matrix.node }} MongoDB ${{ matrix.mongo }}
    steps:
      - uses: actions/checkout@a12a3943b4bdde767164f792f33f40b04645d846 # v3

      - name: Setup node
        uses: actions/setup-node@5b52f097d36d4b0b2f94ed6de710023fbb8b2236 # v3.1.0
        with:
          node-version: ${{ matrix.node }}

      - run: npm install

      - name: Setup
        run: |
          wget -q https://downloads.mongodb.org/linux/mongodb-linux-x86_64-${{ matrix.mongo-os }}-${{ matrix.mongo }}.tgz
          tar xf mongodb-linux-x86_64-${{ matrix.mongo-os }}-${{ matrix.mongo }}.tgz
          mkdir -p ./data/db/27017 ./data/db/27000
          printf "\n--timeout 8000" >> ./test/mocha.opts
          ./mongodb-linux-x86_64-${{ matrix.mongo-os }}-${{ matrix.mongo }}/bin/mongod --setParameter ttlMonitorSleepSecs=1 --fork --dbpath ./data/db/27017 --syslog --port 27017
          sleep 2
          ./mongodb-linux-x86_64-${{ matrix.mongo-os }}-${{ matrix.mongo }}/bin/mongod --version
          echo `pwd`/mongodb-linux-x86_64-${{ matrix.mongo-os }}-${{ matrix.mongo }}/bin >> $GITHUB_PATH
      - run: npm test

  test-typescript:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        node: [22]
        os: [ubuntu-22.04]
    name: TypeScript Test
    steps:
      - uses: actions/checkout@a12a3943b4bdde767164f792f33f40b04645d846 # v3

      - name: Setup node
        uses: actions/setup-node@5b52f097d36d4b0b2f94ed6de710023fbb8b2236 # v3.1.0
        with:
          node-version: ${{ matrix.node }}
      - run: npm install
      - run: npm run test-typescript
