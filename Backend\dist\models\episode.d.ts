import mongoose, { Document, Types } from "mongoose";
export interface IEpisode extends Document {
    _id: Types.ObjectId;
    title: string;
    description?: string;
    audioUrl: string;
    duration?: number;
    podcast: Types.ObjectId;
    publishedAt: Date;
    playCount: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface IEpisodeInput {
    title: string;
    description?: string;
    audioUrl: string;
    duration?: number;
    podcast: Types.ObjectId;
    publishedAt?: Date;
    playCount?: number;
}
export declare const Episode: mongoose.Model<IEpisode, {}, {}, {}, mongoose.Document<unknown, {}, IEpisode, {}, {}> & IEpisode & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=episode.d.ts.map