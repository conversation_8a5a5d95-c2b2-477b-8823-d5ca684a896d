name: build

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [ 'lts/*' ]
        mongodb-version: [latest]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Start MongoDB
        uses: supercharge/mongodb-github-action@1.11.0
        with:
          mongodb-version: ${{ matrix.mongodb-version }}

      - name: Install dependencies
        run: npm install

      - name: Run tests
        run: npm test
