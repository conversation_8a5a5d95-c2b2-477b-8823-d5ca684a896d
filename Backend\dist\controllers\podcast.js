import { Podcast } from "../models/podcast";
/**
 * @desc Get all podcasts (paginated)
 * @route GET /api/podcasts
 * @access Public
 */
const getAllPodcasts = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const options = {
            page: parseInt(page, 10),
            limit: parseInt(limit, 10),
            sort: { createdAt: -1 },
            select: "title author description category coverImageUrl createdAt",
        };
        const podcasts = await Podcast.paginate({}, options);
        res.status(200).json({
            totalPodcasts: podcasts.totalDocs,
            totalPages: podcasts.totalPages,
            currentPage: podcasts.page,
            podcasts: podcasts.docs,
        });
    }
    catch (error) {
        console.error("Error fetching podcasts:", error);
        res.status(500).json({ message: "Server error" });
    }
};
/**
 * @desc Get a single podcast by ID (with its episodes)
 * @route GET /api/podcasts/:id
 * @access Public
 */
const getPodcastById = async (req, res) => {
    try {
        const { id } = req.params;
        const podcast = await Podcast.findById(id).populate({
            path: "episodes",
            select: "title description audioUrl duration createdAt",
            options: { sort: { createdAt: -1 } },
        });
        if (!podcast) {
            return res.status(404).json({ message: "Podcast not found" });
        }
        res.status(200).json(podcast);
    }
    catch (error) {
        console.error("Error fetching podcast by ID:", error);
        res.status(500).json({ message: "Server error" });
    }
};
/**
 * @desc Search podcasts by title or filter by category (paginated)
 * @route GET /api/podcasts/search?query=react&category=Technology&page=1&limit=5
 * @access Public
 */
const searchAndFilterByTitleOrCategory = async (req, res) => {
    try {
        const { query = "", category, page = 1, limit = 10 } = req.query;
        // Build filter dynamically
        const filter = {};
        if (query) {
            filter.title = { $regex: query, $options: "i" }; // case-insensitive search
        }
        if (category && category !== "All") {
            filter.category = category;
        }
        const options = {
            page: parseInt(page),
            limit: parseInt(limit),
            sort: { createdAt: -1 },
            select: "title author description category coverImageUrl createdAt",
        };
        const podcasts = await Podcast.paginate(filter, options);
        res.status(200).json({
            totalResults: podcasts.totalDocs,
            totalPages: podcasts.totalPages,
            currentPage: podcasts.page,
            podcasts: podcasts.docs,
        });
    }
    catch (error) {
        console.error("Error searching/filtering podcasts:", error);
        res.status(500).json({ message: "Server error" });
    }
};
export default {
    getAllPodcasts,
    getPodcastById,
    searchAndFilterByTitleOrCategory,
};
//# sourceMappingURL=podcast.js.map