import mongoose, { Document, Types } from "mongoose";
import { UserRole } from "../utils/constants.js";
export interface IUser extends Document {
    _id: Types.ObjectId;
    name: string;
    email: string;
    password: string;
    favorites: Types.ObjectId[];
    recentlyPlayed: Types.ObjectId[];
    avatarUrl?: string;
    role: UserRole;
    createdAt: Date;
    updatedAt: Date;
}
export interface IUserInput {
    name: string;
    email: string;
    password: string;
    favorites?: Types.ObjectId[];
    recentlyPlayed?: Types.ObjectId[];
    avatarUrl?: string;
    role?: UserRole;
}
export declare const User: mongoose.Model<IUser, {}, {}, {}, mongoose.Document<unknown, {}, IUser, {}, {}> & IUser & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=user.d.ts.map