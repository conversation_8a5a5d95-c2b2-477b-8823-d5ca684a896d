import { Express } from "express";
/**
 * Connect to MongoDB database
 * @param app - Express application instance (optional, for future use)
 * @param port - Port number (optional, for future use)
 */
declare const connectDB: (app?: Express, port?: number) => Promise<void>;
/**
 * Get current database connection status
 */
export declare const getConnectionStatus: () => string;
/**
 * Close database connection
 */
export declare const closeConnection: () => Promise<void>;
export default connectDB;
//# sourceMappingURL=db.d.ts.map